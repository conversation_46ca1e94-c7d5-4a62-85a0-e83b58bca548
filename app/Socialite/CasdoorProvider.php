<?php

namespace App\Socialite;

use Lara<PERSON>\Socialite\Two\ProviderInterface;
use SocialiteProviders\Manager\OAuth2\AbstractProvider as SocialiteAbstractProvider;
use SocialiteProviders\Manager\OAuth2\User;

class CasdoorProvider extends SocialiteAbstractProvider implements ProviderInterface
{
    /**
     * The scopes being requested.
     *
     * @var array
     */
    protected $scopes = ['openid', 'profile', 'email'];

    /**
     * The separating character for the requested scopes.
     *
     * @var string
     */
    protected $scopeSeparator = ' ';

    /**
     * Additional configuration keys.
     *
     * @return array
     */
    public static function additionalConfigKeys()
    {
        return ['server_url', 'application_name'];
    }

    /**
     * Get the authentication URL for the provider.
     *
     * @param  string  $state
     * @return string
     */
    protected function getAuthUrl($state)
    {
        $url = $this->getServerUrl() . '/login/oauth/authorize';

        $parameters = [
            'client_id' => $this->getClientId(),
            'redirect_uri' => $this->redirectUrl,
            'response_type' => 'code',
            'scope' => $this->formatScopes($this->getScopes(), $this->scopeSeparator),
            'state' => $state,
        ];

        return $url . '?' . http_build_query($parameters);
    }

    /**
     * Get the token URL for the provider.
     *
     * @return string
     */
    protected function getTokenUrl()
    {
        return $this->getServerUrl() . '/api/login/oauth/access_token';
    }

    /**
     * Get the raw user for the given access token.
     *
     * @param  string  $token
     * @return array
     */
    protected function getUserByToken($token)
    {
        $response = $this->getHttpClient()->get($this->getServerUrl() . '/api/get-account', [
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
            ],
        ]);

        return json_decode($response->getBody(), true);
    }

    /**
     * Map the raw user array to a Socialite User instance.
     *
     * @param  array  $user
     * @return \SocialiteProviders\Manager\OAuth2\User
     */
    protected function mapUserToObject(array $user)
    {
        // Extract user data from nested 'data' object if available
        $userData = $user['data'] ?? $user;

        // Get email from data object, with fallback generation
        $email = $userData['email'] ?? null;
        if (empty($email) && !empty($userData['name'])) {
            $email = $userData['name'] . '@metishon.co';
            \Log::info('Casdoor OAuth: Generated fallback email', [
                'original_name' => $userData['name'],
                'generated_email' => $email
            ]);
        }

        // Log the mapping process for debugging
        \Log::info('Casdoor OAuth: Mapping user data', [
            'has_data_object' => isset($user['data']),
            'data_email' => $userData['email'] ?? 'not_set',
            'data_displayName' => $userData['displayName'] ?? 'not_set',
            'data_avatar' => $userData['avatar'] ?? 'not_set',
            'final_email' => $email
        ]);

        return (new User)->setRaw($user)->map([
            'id'       => $userData['id'] ?? $userData['name'] ?? null,
            'nickname' => $userData['displayName'] ?? $userData['name'] ?? null,
            'name'     => $userData['displayName'] ?? $userData['name'] ?? null,
            'email'    => $email,
            'avatar'   => $userData['avatar'] ?? null,
        ]);
    }

    /**
     * Get the Casdoor server URL.
     *
     * @return string
     */
    protected function getServerUrl()
    {
        // Get from config only to avoid facade initialization issues
        return $this->getConfig('server_url', '');
    }

    /**
     * Get the client ID for the provider.
     *
     * @return string
     */
    public function getClientId()
    {
        // Get from config only to avoid facade initialization issues
        return $this->getConfig('client_id', '');
    }

    /**
     * Get the client secret for the provider.
     *
     * @return string
     */
    public function getClientSecret()
    {
        // Get from config only to avoid facade initialization issues
        return $this->getConfig('client_secret', '');
    }

    /**
     * Get the application name for Casdoor.
     *
     * @return string
     */
    public function getApplicationName()
    {
        // Get from config only to avoid facade initialization issues
        return $this->getConfig('application_name', '');
    }
}
