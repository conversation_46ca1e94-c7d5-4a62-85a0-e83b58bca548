<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ config()->get('direction') }}">
    
    <head>

        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        {{-- Generate seo tags --}}
        {!! SEO::generate() !!}

        {{-- Favicon --}}
        <link rel="icon" type="image/png" href="{{ src( settings('general')->favicon ) }}"/>

        {{-- Fonts --}}
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

        {{-- Livewire styles --}}
        @livewireStyles

        {{-- Styles --}}
        <link href="{{ mix('css/app.css') }}" rel="stylesheet">

        {{-- Custom fonts --}}
		{!! settings('appearance')->font_link !!}

		{{-- Custom css --}}
        <style>
            :root {
                --color-primary  : {{ settings('appearance')->colors['primary'] }};
                --color-primary-h: {{ hex2hsl( settings('appearance')->colors['primary'] )[0] }};
                --color-primary-s: {{ hex2hsl( settings('appearance')->colors['primary'] )[1] }}%;
                --color-primary-l: {{ hex2hsl( settings('appearance')->colors['primary'] )[2] }}%;
            }
            html {
                font-family: @php echo settings('appearance')->font_family @endphp, sans-serif !important;
            }

            /* 全屏背景图像 */
            .auth-fullscreen-background {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                z-index: -1;
            }

            /* 增强的毛玻璃效果 - Glassmorphism */
            .auth-glassmorphism {
                background: rgba(255, 255, 255, 0.5);
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
                border-radius: 16px;
            }

            /* 暗色模式下的毛玻璃效果 */
            .dark .auth-glassmorphism {
                background: rgba(20, 20, 20, 0.5);
                border: 1px solid rgba(255, 255, 255, 0.15);
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
            }

            /* 确保文字对比度 */
            .auth-glassmorphism h2 {
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            .dark .auth-glassmorphism h2 {
                text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
            }

            /* 响应式设计 */
            @media (max-width: 640px) {
                .auth-glassmorphism {
                    margin: 1rem;
                    padding: 1.5rem;
                    border-radius: 12px;
                }
            }

            /* 确保在没有背景图时有fallback */
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            /* 增强输入框在毛玻璃背景上的可读性 */
            .auth-glassmorphism input,
            .auth-glassmorphism textarea,
            .auth-glassmorphism select {
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            .dark .auth-glassmorphism input,
            .dark .auth-glassmorphism textarea,
            .dark .auth-glassmorphism select {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: white;
            }
        </style>

        {{-- Styles --}}
        @stack('styles')

        {{-- JavaScript variables --}}
        <script>
            __var_app_url        = "{{ url('/') }}";
            __var_app_locale     = "{{ app()->getLocale() }}";
            __var_rtl            = @js(config()->get('direction') === 'ltr' ? false : true);
            __var_primary_color  = "{{ settings('appearance')->colors['primary'] }}";
            __var_axios_base_url = "{{ url('/') }}/";
            __var_currency_code  = "{{ settings('currency')->code }}";
        </script>

        {{-- Custom head code --}}
        @if (settings('appearance')->custom_code_head_main_layout)
            {!! settings('appearance')->custom_code_head_main_layout !!}
        @endif

    </head>

    <body class="antialiased bg-gray-50 dark:bg-[#161616] text-gray-600 min-h-full flex flex-col application application-ltr overflow-x-hidden {{ app()->getLocale() === 'ar' ? 'application-ar' : '' }}">

        {{-- Notification --}}
        <x-notifications position="top-center" z-index="z-[60]" />

        {{-- 全屏背景图像 --}}
        @if(settings('auth')->wallpaper)
            <div class="auth-fullscreen-background" style="background-image: url({{ src(settings('auth')->wallpaper) }})"></div>
        @endif

        {{-- Container --}}
        <div class="min-h-screen flex items-center justify-center relative">

            {{-- Logo --}}
            <div class="fixed top-0 ltr:left-0 rtl:right-0 p-6 lg:px-12 z-10">
                @if (settings('general')->logo_dark)
                    <a href="{{ url('/') }}" class="flex items-center">
                        <img src="{{ src(settings('general')->logo_dark) }}" alt="{{ settings('general')->title }}" style="height: {{ settings('appearance')->sizes['header_desktop_logo_height'] }}px;" class="drop-shadow-lg">
                    </a>
                @else
                    <a href="{{ url('/') }}" class="flex items-center">
                        <img src="{{ src(settings('general')->logo) }}" alt="{{ settings('general')->title }}" style="height: {{ settings('appearance')->sizes['header_desktop_logo_height'] }}px;" class="drop-shadow-lg">
                    </a>
                @endif
            </div>

            {{-- 表单容器 --}}
            <main class="w-full max-w-md mx-4 auth-glassmorphism p-8">
                @yield('content')
            </main>

            {{-- Copyrights --}}
            <div class="fixed bottom-0 ltr:left-0 rtl:right-0 p-6 lg:px-12 text-white font-normal text-[13px] z-10">
                <div class="bg-black bg-opacity-30 rounded-lg px-3 py-1 backdrop-blur-sm">
                    {!! settings('footer')->copyrights !!}
                </div>
            </div>

        </div>

        {{-- Livewire scripts --}}
        @livewireScripts

        {{-- Wire UI --}}
        <wireui:scripts />

        {{-- Core Js --}}
        <script defer src="{{ mix('js/app.js') }}"></script>

        {{-- Helpers --}}
        <script src="{{ url('public/js/utils.js') }}"></script>

        {{-- Custom scripts --}}
        @stack('scripts')

        {{-- Custom footer code --}}
        @if (settings('appearance')->custom_code_footer_main_layout)
            {!! settings('appearance')->custom_code_footer_main_layout !!}
        @endif

    </body>

</html>