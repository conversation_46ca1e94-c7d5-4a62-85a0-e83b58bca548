9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:19:"App\Models\Language":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"languages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:1;s:13:"language_code";s:2:"en";s:12:"country_code";s:2:"us";s:4:"name";s:7:"English";s:9:"is_active";i:1;s:9:"force_rtl";i:0;s:22:"frontend_timing_locale";s:2:"en";s:21:"backend_timing_locale";s:5:"en_US";s:10:"created_at";s:19:"2022-09-01 19:58:48";}s:11:" * original";a:9:{s:2:"id";i:1;s:13:"language_code";s:2:"en";s:12:"country_code";s:2:"us";s:4:"name";s:7:"English";s:9:"is_active";i:1;s:9:"force_rtl";i:0;s:22:"frontend_timing_locale";s:2:"en";s:21:"backend_timing_locale";s:5:"en_US";s:10:"created_at";s:19:"2022-09-01 19:58:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:13:"language_code";i:1;s:12:"country_code";i:2;s:4:"name";i:3;s:9:"is_active";i:4;s:9:"force_rtl";i:5;s:21:"backend_timing_locale";i:6;s:22:"frontend_timing_locale";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:19:"App\Models\Language":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"languages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:2;s:13:"language_code";s:2:"cn";s:12:"country_code";s:2:"cn";s:4:"name";s:12:"中文简体";s:9:"is_active";i:1;s:9:"force_rtl";i:0;s:22:"frontend_timing_locale";s:5:"zh-cn";s:21:"backend_timing_locale";s:5:"zh_CN";s:10:"created_at";s:19:"2025-07-15 06:50:52";}s:11:" * original";a:9:{s:2:"id";i:2;s:13:"language_code";s:2:"cn";s:12:"country_code";s:2:"cn";s:4:"name";s:12:"中文简体";s:9:"is_active";i:1;s:9:"force_rtl";i:0;s:22:"frontend_timing_locale";s:5:"zh-cn";s:21:"backend_timing_locale";s:5:"zh_CN";s:10:"created_at";s:19:"2025-07-15 06:50:52";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:13:"language_code";i:1;s:12:"country_code";i:2;s:4:"name";i:3;s:9:"is_active";i:4;s:9:"force_rtl";i:5;s:21:"backend_timing_locale";i:6;s:22:"frontend_timing_locale";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}