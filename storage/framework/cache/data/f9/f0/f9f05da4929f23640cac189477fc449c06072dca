9999999999O:23:"App\Models\SettingsHero":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"settings_hero";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1;s:13:"enable_bg_img";i:1;s:11:"bg_large_id";i:2;s:12:"bg_medium_id";N;s:11:"bg_small_id";N;s:8:"bg_color";s:7:"#a1a1aa";s:15:"bg_large_height";i:300;s:15:"bg_small_height";i:100;}s:11:" * original";a:8:{s:2:"id";i:1;s:13:"enable_bg_img";i:1;s:11:"bg_large_id";i:2;s:12:"bg_medium_id";N;s:11:"bg_small_id";N;s:8:"bg_color";s:7:"#a1a1aa";s:15:"bg_large_height";i:300;s:15:"bg_small_height";i:100;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:3:{s:16:"background_large";O:22:"App\Models\FileManager":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:12:"file_manager";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:2;s:3:"uid";s:20:"07F5B0EC652171E7F2D1";s:11:"file_folder";s:9:"site/hero";s:9:"file_size";s:6:"649240";s:13:"file_mimetype";s:10:"image/jpeg";s:14:"file_extension";s:4:"webp";s:14:"storage_driver";s:5:"local";s:13:"uploader_type";s:0:"";s:11:"uploader_id";i:0;s:11:"uploaded_at";s:19:"2025-07-16 06:32:45";}s:11:" * original";a:10:{s:2:"id";i:2;s:3:"uid";s:20:"07F5B0EC652171E7F2D1";s:11:"file_folder";s:9:"site/hero";s:9:"file_size";s:6:"649240";s:13:"file_mimetype";s:10:"image/jpeg";s:14:"file_extension";s:4:"webp";s:14:"storage_driver";s:5:"local";s:13:"uploader_type";s:0:"";s:11:"uploader_id";i:0;s:11:"uploaded_at";s:19:"2025-07-16 06:32:45";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:3:"uid";i:1;s:11:"file_folder";i:2;s:9:"file_size";i:3;s:13:"file_mimetype";i:4;s:14:"file_extension";i:5;s:13:"uploader_type";i:6;s:11:"uploader_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:17:"background_medium";N;s:16:"background_small";N;}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:13:"enable_bg_img";i:1;s:11:"bg_large_id";i:2;s:12:"bg_medium_id";i:3;s:11:"bg_small_id";i:4;s:8:"bg_color";i:5;s:15:"bg_large_height";i:6;s:15:"bg_small_height";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}