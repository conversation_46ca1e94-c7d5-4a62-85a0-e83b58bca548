9999999999O:26:"App\Models\ProjectSettings":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"projects_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:1;s:10:"is_enabled";i:0;s:21:"auto_approve_projects";i:1;s:17:"auto_approve_bids";i:1;s:15:"is_free_posting";i:1;s:18:"is_premium_posting";i:0;s:18:"is_premium_bidding";i:0;s:15:"commission_type";s:10:"percentage";s:26:"commission_from_freelancer";s:3:"2.3";s:25:"commission_from_publisher";s:3:"2.2";s:12:"who_can_post";s:4:"both";}s:11:" * original";a:11:{s:2:"id";i:1;s:10:"is_enabled";i:0;s:21:"auto_approve_projects";i:1;s:17:"auto_approve_bids";i:1;s:15:"is_free_posting";i:1;s:18:"is_premium_posting";i:0;s:18:"is_premium_bidding";i:0;s:15:"commission_type";s:10:"percentage";s:26:"commission_from_freelancer";s:3:"2.3";s:25:"commission_from_publisher";s:3:"2.2";s:12:"who_can_post";s:4:"both";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:10:"is_enabled";i:1;s:21:"auto_approve_projects";i:2;s:17:"auto_approve_bids";i:3;s:15:"is_free_posting";i:4;s:18:"is_premium_posting";i:5;s:18:"is_premium_bidding";i:6;s:15:"commission_type";i:7;s:26:"commission_from_freelancer";i:8;s:25:"commission_from_publisher";i:9;s:12:"who_can_post";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}