<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['id', 'text']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['id', 'text']); ?>
<?php foreach (array_filter((['id', 'text']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div>
    <div id="<?php echo e($id); ?>" role="tooltip" class="inline-block absolute invisible z-10 py-2 px-3 text-xs font-medium text-white bg-black/80 rounded-lg shadow-sm opacity-0 tooltip backdrop-blur-md dark:bg-black/80">
        <?php echo e($text); ?>

    </div>
</div><?php /**PATH /www/wwwroot/vup.gifts/resources/views/components/forms/tooltip.blade.php ENDPATH**/ ?>