<!DOCTYPE html>
<html lang="<?php echo e(app()->getLocale()); ?>" dir="<?php echo e(config()->get('direction')); ?>">
    
    <head>

        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        
        <?php echo SEO::generate(); ?>


        
        <link rel="icon" type="image/png" href="<?php echo e(src( settings('general')->favicon )); ?>"/>

        
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

        
        <?php echo \Livewire\Livewire::styles(); ?>


        
        <link href="<?php echo e(mix('css/app.css')); ?>" rel="stylesheet">

        
		<?php echo settings('appearance')->font_link; ?>


		
        <style>
            :root {
                --color-primary  : <?php echo e(settings('appearance')->colors['primary']); ?>;
                --color-primary-h: <?php echo e(hex2hsl( settings('appearance')->colors['primary'] )[0]); ?>;
                --color-primary-s: <?php echo e(hex2hsl( settings('appearance')->colors['primary'] )[1]); ?>%;
                --color-primary-l: <?php echo e(hex2hsl( settings('appearance')->colors['primary'] )[2]); ?>%;
            }
            html {
                font-family: <?php echo settings('appearance')->font_family ?>, sans-serif !important;
            }

            /* 全屏背景图像 */
            .auth-fullscreen-background {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                z-index: -1;
            }

            /* 增强的毛玻璃效果 - Glassmorphism */
            .auth-glassmorphism {
                background: rgba(255, 255, 255, 0.5);
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
                border-radius: 16px;
            }

            /* 暗色模式下的毛玻璃效果 */
            .dark .auth-glassmorphism {
                background: rgba(20, 20, 20, 0.5);
                border: 1px solid rgba(255, 255, 255, 0.15);
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
            }

            /* 确保文字对比度 */
            .auth-glassmorphism h2 {
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            .dark .auth-glassmorphism h2 {
                text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
            }

            /* 响应式设计 */
            @media (max-width: 640px) {
                .auth-glassmorphism {
                    margin: 1rem;
                    padding: 1.5rem;
                    border-radius: 12px;
                }
            }

            /* 确保在没有背景图时有fallback */
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            /* 增强输入框在毛玻璃背景上的可读性 */
            .auth-glassmorphism input,
            .auth-glassmorphism textarea,
            .auth-glassmorphism select {
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            .dark .auth-glassmorphism input,
            .dark .auth-glassmorphism textarea,
            .dark .auth-glassmorphism select {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: white;
            }
        </style>

        
        <?php echo $__env->yieldPushContent('styles'); ?>

        
        <script>
            __var_app_url        = "<?php echo e(url('/')); ?>";
            __var_app_locale     = "<?php echo e(app()->getLocale()); ?>";
            __var_rtl            = <?php echo \Illuminate\Support\Js::from(config()->get('direction') === 'ltr' ? false : true)->toHtml() ?>;
            __var_primary_color  = "<?php echo e(settings('appearance')->colors['primary']); ?>";
            __var_axios_base_url = "<?php echo e(url('/')); ?>/";
            __var_currency_code  = "<?php echo e(settings('currency')->code); ?>";
        </script>

        
        <?php if(settings('appearance')->custom_code_head_main_layout): ?>
            <?php echo settings('appearance')->custom_code_head_main_layout; ?>

        <?php endif; ?>

    </head>

    <body class="antialiased bg-gray-50 dark:bg-[#161616] text-gray-600 min-h-full flex flex-col application application-ltr overflow-x-hidden <?php echo e(app()->getLocale() === 'ar' ? 'application-ar' : ''); ?>">

        
        <?php if (isset($component)) { $__componentOriginal92d1a160a4445015711a1d3715ec46524d39b3ba = $component; } ?>
<?php $component = WireUi\View\Components\Notifications::resolve(['position' => 'top-center','zIndex' => 'z-[60]'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('notifications'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(WireUi\View\Components\Notifications::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal92d1a160a4445015711a1d3715ec46524d39b3ba)): ?>
<?php $component = $__componentOriginal92d1a160a4445015711a1d3715ec46524d39b3ba; ?>
<?php unset($__componentOriginal92d1a160a4445015711a1d3715ec46524d39b3ba); ?>
<?php endif; ?>

        
        <?php if(settings('auth')->wallpaper): ?>
            <div class="auth-fullscreen-background" style="background-image: url(<?php echo e(src(settings('auth')->wallpaper)); ?>)"></div>
        <?php endif; ?>

        
        <div class="min-h-screen flex items-center justify-center relative">

            
            <div class="fixed top-0 ltr:left-0 rtl:right-0 p-6 lg:px-12 z-10">
                <?php if(settings('general')->logo_dark): ?>
                    <a href="<?php echo e(url('/')); ?>" class="flex items-center">
                        <img src="<?php echo e(src(settings('general')->logo_dark)); ?>" alt="<?php echo e(settings('general')->title); ?>" style="height: <?php echo e(settings('appearance')->sizes['header_desktop_logo_height']); ?>px;" class="drop-shadow-lg">
                    </a>
                <?php else: ?>
                    <a href="<?php echo e(url('/')); ?>" class="flex items-center">
                        <img src="<?php echo e(src(settings('general')->logo)); ?>" alt="<?php echo e(settings('general')->title); ?>" style="height: <?php echo e(settings('appearance')->sizes['header_desktop_logo_height']); ?>px;" class="drop-shadow-lg">
                    </a>
                <?php endif; ?>
            </div>

            
            <main class="w-full max-w-md mx-4 auth-glassmorphism p-8">
                <?php echo $__env->yieldContent('content'); ?>
            </main>

            
            <div class="fixed bottom-0 ltr:left-0 rtl:right-0 p-6 lg:px-12 text-white font-normal text-[13px] z-10">
                <div class="bg-black bg-opacity-30 rounded-lg px-3 py-1 backdrop-blur-sm">
                    <?php echo settings('footer')->copyrights; ?>

                </div>
            </div>

        </div>

        
        <?php echo \Livewire\Livewire::scripts(); ?>


        
        <script >window.Wireui = {hook(hook, callback) {window.addEventListener(`wireui:${hook}`, () => callback())},dispatchHook(hook) {window.dispatchEvent(new Event(`wireui:${hook}`))}}</script>
<script src="https://vup.gifts/wireui/assets/scripts?id=3c15fb3b36f54e2baae1e97b6eb0015e" defer ></script>

        
        <script defer src="<?php echo e(mix('js/app.js')); ?>"></script>

        
        <script src="<?php echo e(url('public/js/utils.js')); ?>"></script>

        
        <?php echo $__env->yieldPushContent('scripts'); ?>

        
        <?php if(settings('appearance')->custom_code_footer_main_layout): ?>
            <?php echo settings('appearance')->custom_code_footer_main_layout; ?>

        <?php endif; ?>

    </body>

</html><?php /**PATH /www/wwwroot/vup.gifts/resources/views/livewire/main/auth/layout/auth.blade.php ENDPATH**/ ?>